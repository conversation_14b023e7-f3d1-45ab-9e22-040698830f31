# 资源上传API文档

## 概述

资源上传API允许用户将图片或其他类型的文件上传到七牛云存储。上传过程分为两步：
1. 获取七牛云上传token
2. 将文件上传到七牛云

## API端点

### POST /api/upload

上传文件到七牛云。

#### 请求格式

- **Content-Type**: `multipart/form-data`
- **认证**: 需要登录（使用飞书认证）

#### 请求参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| file   | File | 是   | 要上传的文件 |

#### 支持的文件类型

| 文件类型 | MIME类型 | 扩展名 | 最大大小 |
|----------|----------|--------|----------|
| JPEG图片 | image/jpeg | .jpeg | 10MB |
| JPG图片  | image/jpg | .jpg | 10MB |
| PNG图片  | image/png | .png | 10MB |
| GIF图片  | image/gif | .gif | 10MB |
| WebP图片 | image/webp | .webp | 10MB |
| PDF文档  | application/pdf | .pdf | 10MB |
| 文本文件 | text/plain | .txt | 10MB |
| Word文档 | application/msword | .doc | 10MB |
| Word文档 | application/vnd.openxmlformats-officedocument.wordprocessingml.document | .docx | 10MB |
| Excel表格 | application/vnd.ms-excel | .xls | 10MB |
| Excel表格 | application/vnd.openxmlformats-officedocument.spreadsheetml.sheet | .xlsx | 10MB |

#### 响应格式

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "filename": "chatbi-resource/U2gKqJXKugVpN5uoaDgYEtmVvPmgIccp.jpeg",
    "key": "chatbi-resource/U2gKqJXKugVpN5uoaDgYEtmVvPmgIccp.jpeg",
    "url": "https://cdn.summerfarm.net/chatbi-resource/U2gKqJXKugVpN5uoaDgYEtmVvPmgIccp.jpeg",
    "size": 1024000,
    "content_type": "image/jpeg"
  }
}
```

**错误响应**:
```json
{
  "error": "错误描述"
}
```

#### 错误码

| HTTP状态码 | 错误描述 |
|------------|----------|
| 400 | 请求参数错误（没有文件、文件为空、文件类型不支持等） |
| 401 | 用户未登录 |
| 500 | 服务器内部错误（获取token失败、上传失败等） |

## 使用示例

### JavaScript (前端)

```javascript
// 上传文件
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
            credentials: 'include' // 包含认证cookie
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('上传成功:', result.data.url);
            return result.data.url;
        } else {
            console.error('上传失败:', result.error);
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('上传错误:', error);
        throw error;
    }
}

// 使用示例
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const url = await uploadFile(file);
            console.log('文件URL:', url);
        } catch (error) {
            alert('上传失败: ' + error.message);
        }
    }
});
```

### Python (后端测试)

```python
import requests

def upload_file(file_path, server_url='http://localhost:5700'):
    """上传文件到服务器"""
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(
            f'{server_url}/api/upload',
            files=files,
            cookies={'access_token': 'your_access_token'}  # 需要有效的认证token
        )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            return result['data']['url']
        else:
            raise Exception(result['error'])
    else:
        raise Exception(f'HTTP {response.status_code}: {response.text}')

# 使用示例
try:
    url = upload_file('test_image.jpg')
    print(f'上传成功: {url}')
except Exception as e:
    print(f'上传失败: {e}')
```

### cURL

```bash
# 上传图片文件
curl -X POST http://localhost:5700/api/upload \
  -H "Cookie: access_token=your_access_token" \
  -F "file=@/path/to/your/image.jpg"
```

## 文件命名规则

上传的文件会被重命名为以下格式：
```
chatbi-resource/{32位随机字符}.{原始扩展名}
```

例如：
- `chatbi-resource/U2gKqJXKugVpN5uoaDgYEtmVvPmgIccp.jpeg`
- `chatbi-resource/r4U0pPvI1LUdHe444UduyTAt0GhuWNIS.png`

这样可以确保：
1. 文件名唯一性，避免冲突
2. 安全性，防止恶意文件名
3. 统一的存储路径管理

## 技术实现

### 上传流程

1. **文件验证**：检查文件大小、类型等
2. **生成随机文件名**：使用32位随机字符
3. **获取七牛云token**：调用 `https://admin.summerfarm.net/qiniu/upload-token/one`
4. **上传到七牛云**：使用获取的token上传文件到 `https://up-z0.qiniup.com/`
5. **返回文件URL**：构建最终的文件访问URL

### 安全考虑

- 需要用户登录认证
- 限制文件大小（10MB）
- 限制文件类型
- 随机文件名防止路径遍历攻击
- 详细的错误日志记录

## 注意事项

1. **认证要求**：必须先通过飞书登录才能使用上传功能
2. **文件大小限制**：单个文件最大10MB
3. **文件类型限制**：只支持列表中的文件类型
4. **网络超时**：获取token超时10秒，上传文件超时30秒
5. **错误处理**：所有错误都会记录到日志中，便于调试
